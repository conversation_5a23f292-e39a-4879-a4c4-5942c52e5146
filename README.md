# موقع حراج

موقع إعلانات مبوبة مشابه لموقع حراج.سمية مبني باستخدام PHP الأصلي مع MySQL.

## الميزات

- تسجيل المستخدمين وتسجيل الدخول
- إنشاء وتحرير وحذف الإعلانات
- تحميل الصور للإعلانات
- البحث والتصفية حسب القسم
- لوحة تحكم إدارية
- نظام جلسات آمن
- تصميم متجاوب باستخدام Bootstrap 5

## المتطلبات

- PHP 7.4 أو أعلى
- MySQL 5.7 أو أعلى
- Apache أو Nginx
- Composer (لإدارة الحزم)

## التثبيت

1. استنساخ المستودع:
   ```
   git clone https://github.com/yourusername/haraj.git
   ```

2. إنشاء قاعدة البيانات:
   ```sql
   CREATE DATABASE haraj;
   ```

3. إنشاء الجداول:
   ```sql
   CREATE TABLE users (
       id INT AUTO_INCREMENT PRIMARY KEY,
       name VARCHAR(255) NOT NULL,
       email VARCHAR(255) UNIQUE NOT NULL,
       password VARCHAR(255) NOT NULL,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );

   CREATE TABLE categories (
       id INT AUTO_INCREMENT PRIMARY KEY,
       name VARCHAR(255) NOT NULL,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );

   CREATE TABLE ads (
       id INT AUTO_INCREMENT PRIMARY KEY,
       title VARCHAR(255) NOT NULL,
       description TEXT NOT NULL,
       price DECIMAL(10, 2) NOT NULL,
       image VARCHAR(255),
       category_id INT,
       user_id INT,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       FOREIGN KEY (category_id) REFERENCES categories(id),
       FOREIGN KEY (user_id) REFERENCES users(id)
   );
   ```

4. إضافة بيانات تجريبية:
   ```sql
   INSERT INTO categories (name) VALUES ('السيارات'), ('العقار'), ('الإلكترونيات'), ('الملابس'), ('خدمات');
   ```

5. تكوين ملف .env حسب بيئةك.

## هيكل المشروع

```
haraj/
├── public/                   # المجلد الرئيسي للوصول العام
│   ├── assets/               # الملفات الثابتة
│   │   ├── css/              # ملفات التنسيق
│   │   ├── js/               # ملفات الجافاسكريبت
│   │   ├── images/           # الصور
│   │   └── uploads/          # الملفات المرفوعة من المستخدمين
│   ├── index.php             # نقطة الدخول الوحيدة للتطبيق
│   └── .htaccess             # إعدادات السيرفر
│
├── app/                      # قلب التطبيق
│   ├── Controllers/          # المتحكمات
│   ├── Models/               # النماذج
│   ├── Views/                # واجهات المستخدم
│   ├── Core/                 # الملفات الأساسية
│   └── config/               # إعدادات التطبيق
│
├── vendor/                   # مكتبات الطرف الثالث
├── storage/                  # التخزين الداخلي
└── tests/                    # اختبارات التطبيق
```

## المطور

تم تطوير هذا المشروع بواسطة [اسمك].

## الرخصة

هذا المشروع مرخص تحت رخصة MIT.

<?php
// إدارة الإعلانات

namespace App\Controllers;

use App\Models\Ad;
use App\Models\Category;

class AdController {
    private $ad;
    private $category;

    public function __construct() {
        $this->ad = new Ad();
        $this->category = new Category();
    }

    // عرض جميع الإعلانات
    public function index() {
        $ads = $this->ad->getAll();
        $categories = $this->category->getAll();

        require_once __DIR__ . '/../Views/layouts/header.php';
        require_once __DIR__ . '/../Views/ads/index.php';
        require_once __DIR__ . '/../Views/layouts/footer.php';
    }

    // عرض إعلان محدد
    public function show($id) {
        $ad = $this->ad->findById($id);

        if (!$ad) {
            // إذا لم يتم العثور على الإعلان
            http_response_code(404);
            require_once __DIR__ . '/../Views/errors/404.php';
            return;
        }

        require_once __DIR__ . '/../Views/layouts/header.php';
        require_once __DIR__ . '/../Views/ads/show.php';
        require_once __DIR__ . '/../Views/layouts/footer.php';
    }

    // عرض نموذج إنشاء إعلان جديد
    public function create() {
        // التحقق من تسجيل الدخول
        if (!isset($_SESSION['user_id'])) {
            $_SESSION['error'] = 'يجب تسجيل الدخول لإنشاء إعلان';
            header('Location: /login');
            return;
        }

        $categories = $this->category->getAll();

        require_once __DIR__ . '/../Views/layouts/header.php';
        require_once __DIR__ . '/../Views/ads/create.php';
        require_once __DIR__ . '/../Views/layouts/footer.php';
    }

    // معالجة إنشاء إعلان جديد
    public function store() {
        // التحقق من تسجيل الدخول
        if (!isset($_SESSION['user_id'])) {
            $_SESSION['error'] = 'يجب تسجيل الدخول لإنشاء إعلان';
            header('Location: /login');
            return;
        }

        $title = $_POST['title'];
        $description = $_POST['description'];
        $price = $_POST['price'];
        $category_id = $_POST['category_id'];
        $user_id = $_SESSION['user_id'];

        // التحقق من صحة البيانات
        if (empty($title) || empty($description) || empty($price) || empty($category_id)) {
            $_SESSION['error'] = 'جميع الحقول مطلوبة';
            header('Location: /ads/create');
            return;
        }

        // معالجة رفع الصورة
        $image = null;
        if (!empty($_FILES['image']['name'])) {
            $image = $this->uploadImage($_FILES['image']);
            if (!$image) {
                $_SESSION['error'] = 'حدث خطأ أثناء رفع الصورة';
                header('Location: /ads/create');
                return;
            }
        }

        // إنشاء الإعلان
        $result = $this->ad->create([
            'title' => $title,
            'description' => $description,
            'price' => $price,
            'image' => $image,
            'category_id' => $category_id,
            'user_id' => $user_id
        ]);

        if ($result) {
            $_SESSION['success'] = 'تم إنشاء الإعلان بنجاح';
            header('Location: /ads');
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء إنشاء الإعلان';
            header('Location: /ads/create');
        }
    }

    // عرض نموذج تحرير إعلان
    public function edit($id) {
        // التحقق من تسجيل الدخول
        if (!isset($_SESSION['user_id'])) {
            $_SESSION['error'] = 'يجب تسجيل الدخول لتحرير الإعلان';
            header('Location: /login');
            return;
        }

        $ad = $this->ad->findById($id);

        // التحقق من وجود الإعلان
        if (!$ad) {
            http_response_code(404);
            require_once __DIR__ . '/../Views/errors/404.php';
            return;
        }

        // التحقق من أن المستخدم هو صاحب الإعلان
        if ($ad->user_id != $_SESSION['user_id']) {
            $_SESSION['error'] = 'ليس لديك صلاحية لتحرير هذا الإعلان';
            header('Location: /ads');
            return;
        }

        $categories = $this->category->getAll();

        require_once __DIR__ . '/../Views/layouts/header.php';
        require_once __DIR__ . '/../Views/ads/edit.php';
        require_once __DIR__ . '/../Views/layouts/footer.php';
    }

    // معالجة تحرير إعلان
    public function update($id) {
        // التحقق من تسجيل الدخول
        if (!isset($_SESSION['user_id'])) {
            $_SESSION['error'] = 'يجب تسجيل الدخول لتحرير الإعلان';
            header('Location: /login');
            return;
        }

        $ad = $this->ad->findById($id);

        // التحقق من وجود الإعلان
        if (!$ad) {
            http_response_code(404);
            require_once __DIR__ . '/../Views/errors/404.php';
            return;
        }

        // التحقق من أن المستخدم هو صاحب الإعلان
        if ($ad->user_id != $_SESSION['user_id']) {
            $_SESSION['error'] = 'ليس لديك صلاحية لتحرير هذا الإعلان';
            header('Location: /ads');
            return;
        }

        $title = $_POST['title'];
        $description = $_POST['description'];
        $price = $_POST['price'];
        $category_id = $_POST['category_id'];

        // التحقق من صحة البيانات
        if (empty($title) || empty($description) || empty($price) || empty($category_id)) {
            $_SESSION['error'] = 'جميع الحقول مطلوبة';
            header('Location: /ads/' . $id . '/edit');
            return;
        }

        // معالجة رفع الصورة
        $image = $ad->image;
        if (!empty($_FILES['image']['name'])) {
            $image = $this->uploadImage($_FILES['image']);
            if (!$image) {
                $_SESSION['error'] = 'حدث خطأ أثناء رفع الصورة';
                header('Location: /ads/' . $id . '/edit');
                return;
            }
        }

        // تحديث الإعلان
        $result = $this->ad->update($id, [
            'title' => $title,
            'description' => $description,
            'price' => $price,
            'image' => $image,
            'category_id' => $category_id
        ]);

        if ($result) {
            $_SESSION['success'] = 'تم تحديث الإعلان بنجاح';
            header('Location: /ads/' . $id);
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء تحديث الإعلان';
            header('Location: /ads/' . $id . '/edit');
        }
    }

    // حذف إعلان
    public function delete($id) {
        // التحقق من تسجيل الدخول
        if (!isset($_SESSION['user_id'])) {
            $_SESSION['error'] = 'يجب تسجيل الدخول لحذف الإعلان';
            header('Location: /login');
            return;
        }

        $ad = $this->ad->findById($id);

        // التحقق من وجود الإعلان
        if (!$ad) {
            http_response_code(404);
            require_once __DIR__ . '/../Views/errors/404.php';
            return;
        }

        // التحقق من أن المستخدم هو صاحب الإعلان
        if ($ad->user_id != $_SESSION['user_id']) {
            $_SESSION['error'] = 'ليس لديك صلاحية لحذف هذا الإعلان';
            header('Location: /ads');
            return;
        }

        // حذف الصورة إذا كانت موجودة
        if ($ad->image && file_exists(UPLOAD_PATH . $ad->image)) {
            unlink(UPLOAD_PATH . $ad->image);
        }

        // حذف الإعلان
        $result = $this->ad->delete($id);

        if ($result) {
            $_SESSION['success'] = 'تم حذف الإعلان بنجاح';
            header('Location: /ads');
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء حذف الإعلان';
            header('Location: /ads');
        }
    }

    // رفع الصورة
    private function uploadImage($file) {
        $targetDir = UPLOAD_PATH;
        $fileName = uniqid() . '_' . basename($file["name"]);
        $targetFile = $targetDir . $fileName;
        $imageFileType = strtolower(pathinfo($targetFile, PATHINFO_EXTENSION));

        // التحقق من أن الملف هو صورة فعلية
        $check = getimagesize($file["tmp_name"]);
        if ($check === false) {
            return false;
        }

        // التحقق من حجم الملف (أقصى حجم 5 ميغابايت)
        if ($file["size"] > 5000000) {
            return false;
        }

        // السماح ببعض أنواع الصور فقط
        if ($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg" && $imageFileType != "gif") {
            return false;
        }

        // رفع الملف
        if (move_uploaded_file($file["tmp_name"], $targetFile)) {
            return $fileName;
        } else {
            return false;
        }
    }
}

<?php
// لوحة التحكم الإدارية

namespace App\Controllers;

use App\Models\User;
use App\Models\Ad;
use App\Models\Category;

class AdminController {
    private $user;
    private $ad;
    private $category;

    public function __construct() {
        $this->user = new User();
        $this->ad = new Ad();
        $this->category = new Category();
    }

    // عرض لوحة التحكم الرئيسية
    public function index() {
        // التحقق من تسجيل الدخول كمسؤول
        if (!isset($_SESSION['user_id']) || $_SESSION['user_id'] != 1) { // افتراض أن المستخدم صاحب ID = 1 هو المدير
            $_SESSION['error'] = 'ليس لديك صلاحية الوصول إلى لوحة التحكم';
            header('Location: /');
            return;
        }

        $usersCount = count($this->user->getAll());
        $adsCount = count($this->ad->getAll());
        $categoriesCount = count($this->category->getAll());

        require_once __DIR__ . '/../Views/layouts/header.php';
        require_once __DIR__ . '/../Views/admin/index.php';
        require_once __DIR__ . '/../Views/layouts/footer.php';
    }

    // عرض جميع الإعلانات في لوحة التحكم
    public function ads() {
        // التحقق من تسجيل الدخول كمسؤول
        if (!isset($_SESSION['user_id']) || $_SESSION['user_id'] != 1) {
            $_SESSION['error'] = 'ليس لديك صلاحية الوصول إلى لوحة التحكم';
            header('Location: /');
            return;
        }

        $ads = $this->ad->getAll();

        require_once __DIR__ . '/../Views/layouts/header.php';
        require_once __DIR__ . '/../Views/admin/ads.php';
        require_once __DIR__ . '/../Views/layouts/footer.php';
    }

    // عرض جميع المستخدمين في لوحة التحكم
    public function users() {
        // التحقق من تسجيل الدخول كمسؤول
        if (!isset($_SESSION['user_id']) || $_SESSION['user_id'] != 1) {
            $_SESSION['error'] = 'ليس لديك صلاحية الوصول إلى لوحة التحكم';
            header('Location: /');
            return;
        }

        $users = $this->user->getAll();

        require_once __DIR__ . '/../Views/layouts/header.php';
        require_once __DIR__ . '/../Views/admin/users.php';
        require_once __DIR__ . '/../Views/layouts/footer.php';
    }

    // حذف إعلان من لوحة التحكم
    public function deleteAd($id) {
        // التحقق من تسجيل الدخول كمسؤول
        if (!isset($_SESSION['user_id']) || $_SESSION['user_id'] != 1) {
            $_SESSION['error'] = 'ليس لديك صلاحية الوصول إلى لوحة التحكم';
            header('Location: /');
            return;
        }

        $ad = $this->ad->findById($id);

        // التحقق من وجود الإعلان
        if (!$ad) {
            $_SESSION['error'] = 'الإعلان غير موجود';
            header('Location: /admin/ads');
            return;
        }

        // حذف الصورة إذا كانت موجودة
        if ($ad->image && file_exists(UPLOAD_PATH . $ad->image)) {
            unlink(UPLOAD_PATH . $ad->image);
        }

        // حذف الإعلان
        $result = $this->ad->delete($id);

        if ($result) {
            $_SESSION['success'] = 'تم حذف الإعلان بنجاح';
            header('Location: /admin/ads');
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء حذف الإعلان';
            header('Location: /admin/ads');
        }
    }

    // حذف مستخدم من لوحة التحكم
    public function deleteUser($id) {
        // التحقق من تسجيل الدخول كمسؤول
        if (!isset($_SESSION['user_id']) || $_SESSION['user_id'] != 1) {
            $_SESSION['error'] = 'ليس لديك صلاحية الوصول إلى لوحة التحكم';
            header('Location: /');
            return;
        }

        // منع حذف الحساب الإداري
        if ($id == 1) {
            $_SESSION['error'] = 'لا يمكن حذف الحساب الإداري';
            header('Location: /admin/users');
            return;
        }

        // حذف المستخدم
        $result = $this->user->delete($id);

        if ($result) {
            $_SESSION['success'] = 'تم حذف المستخدم بنجاح';
            header('Location: /admin/users');
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء حذف المستخدم';
            header('Location: /admin/users');
        }
    }
}

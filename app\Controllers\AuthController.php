<?php
// تسجيل الدخول/تسجيل/تسجيل خروج

namespace App\Controllers;

use App\Models\User;

class AuthController {
    private $user;

    public function __construct() {
        $this->user = new User();
    }

    // عرض صفحة تسجيل الدخول
    public function showLogin() {
        require_once __DIR__ . '/../Views/layouts/header.php';
        require_once __DIR__ . '/../Views/auth/login.php';
        require_once __DIR__ . '/../Views/layouts/footer.php';
    }

    // معالجة تسجيل الدخول
    public function login() {
        $email = $_POST['email'];
        $password = $_POST['password'];

        // التحقق من صحة البيانات
        if (empty($email) || empty($password)) {
            $_SESSION['error'] = 'جميع الحقول مطلوبة';
            header('Location: /login');
            return;
        }

        // التحقق من بيانات المستخدم
        $user = $this->user->findByEmail($email);

        if ($user && password_verify($password, $user->password)) {
            // تسجيل الدخول الناجح
            $_SESSION['user_id'] = $user->id;
            $_SESSION['user_name'] = $user->name;
            $_SESSION['success'] = 'تم تسجيل الدخول بنجاح';
            header('Location: /');
        } else {
            // تسجيل الدخول الفاشل
            $_SESSION['error'] = 'بيانات الدخول غير صحيحة';
            header('Location: /login');
        }
    }

    // عرض صفحة التسجيل
    public function showRegister() {
        require_once __DIR__ . '/../Views/layouts/header.php';
        require_once __DIR__ . '/../Views/auth/register.php';
        require_once __DIR__ . '/../Views/layouts/footer.php';
    }

    // معالجة التسجيل
    public function register() {
        $name = $_POST['name'];
        $email = $_POST['email'];
        $password = $_POST['password'];
        $confirmPassword = $_POST['confirm_password'];

        // التحقق من صحة البيانات
        if (empty($name) || empty($email) || empty($password) || empty($confirmPassword)) {
            $_SESSION['error'] = 'جميع الحقول مطلوبة';
            header('Location: /register');
            return;
        }

        if ($password !== $confirmPassword) {
            $_SESSION['error'] = 'كلمة المرور غير متطابقة';
            header('Location: /register');
            return;
        }

        // التحقق من وجود البريد الإلكتروني مسبقاً
        if ($this->user->findByEmail($email)) {
            $_SESSION['error'] = 'البريد الإلكتروني مسجل مسبقاً';
            header('Location: /register');
            return;
        }

        // إنشاء حساب جديد
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $result = $this->user->create([
            'name' => $name,
            'email' => $email,
            'password' => $hashedPassword
        ]);

        if ($result) {
            $_SESSION['success'] = 'تم إنشاء الحساب بنجاح';
            header('Location: /login');
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء إنشاء الحساب';
            header('Location: /register');
        }
    }

    // تسجيل الخروج
    public function logout() {
        session_destroy();
        $_SESSION['success'] = 'تم تسجيل الخروج بنجاح';
        header('Location: /');
    }
}

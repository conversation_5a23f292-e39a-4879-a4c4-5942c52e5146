<?php
// البحث والتصفية

namespace App\Controllers;

use App\Models\Ad;
use App\Models\Category;

class SearchController {
    private $ad;
    private $category;

    public function __construct() {
        $this->ad = new Ad();
        $this->category = new Category();
    }

    // عرض نتائج البحث
    public function index() {
        $keyword = isset($_GET['q']) ? $_GET['q'] : '';
        $category_id = isset($_GET['category_id']) ? $_GET['category_id'] : '';

        // الحصول على جميع الأقسام
        $categories = $this->category->getAll();

        // البحث في الإعلانات
        if (!empty($keyword) || !empty($category_id)) {
            if (!empty($category_id)) {
                $ads = $this->ad->getByCategoryId($category_id);
            } else {
                $ads = $this->ad->search($keyword);
            }
        } else {
            $ads = $this->ad->getAll();
        }

        require_once __DIR__ . '/../Views/layouts/header.php';
        require_once __DIR__ . '/../Views/ads/search.php';
        require_once __DIR__ . '/../Views/layouts/footer.php';
    }
}

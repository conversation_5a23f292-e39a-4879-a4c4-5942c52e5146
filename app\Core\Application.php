<?php
// تهيئة التطبيق

namespace App\Core;

class Application {
    private $router;
    private $session;

    public function __construct() {
        // تهيئة الجلسة
        $this->session = new Session();
        $this->session->start();

        // تهيئة التوجيه
        $this->router = new Router();
    }

    public function run() {
        // تحميل المسارات
        require_once __DIR__ . '/../config/routes.php';

        // معالجة الطلب
        $this->router->resolve();
    }
}

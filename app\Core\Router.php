<?php
// توجيه الطلبات

namespace App\Core;

class Router {
    private $routes = [];
    private $requestUri;
    private $requestMethod;

    public function __construct() {
        // الحصول على عنوان URL المطلوب
        $this->requestUri = $this->getRequestUri();
        // الحصول على نوع الطلب (GET, POST, إلخ)
        $this->requestMethod = $_SERVER['REQUEST_METHOD'];
    }

    // إضافة مسار جديد
    public function add($uri, $callback, $method = 'GET') {
        $this->routes[] = [
            'uri' => $uri,
            'callback' => $callback,
            'method' => $method
        ];
    }

    // معالجة الطلب وإيجاد المسار المناسب
    public function resolve() {
        foreach ($this->routes as $route) {
            // التحقق من نوع الطلب
            if ($route['method'] !== $this->requestMethod) {
                continue;
            }

            // مطابقة المسار
            if ($this->match($route['uri'], $this->requestUri)) {
                // تنفيذ الدالة المرتبطة بالمسار
                $this->executeCallback($route['callback']);
                return;
            }
        }

        // إذا لم يتم العثور على مسار مناسب
        $this->notFound();
    }

    // مطابقة المسار
    private function match($routeUri, $requestUri) {
        // تحويل المسار إلى تعبير منتظم
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $routeUri);
        $pattern = '#^' . $pattern . '$#';

        // مطابقة التعبير المنتظم
        if (preg_match($pattern, $requestUri, $matches)) {
            // حذف العنصر الأول من المصفوفة (المسار كاملاً)
            array_shift($matches);

            // تخزين المتغيرات في الطلب
            $_REQUEST = array_merge($_REQUEST, $matches);
            return true;
        }

        return false;
    }

    // تنفيذ الدالة المرتبطة بالمسار
    private function executeCallback($callback) {
        if (is_callable($callback)) {
            // إذا كانت الدالة قابلة للاستدعاء مباشرة
            call_user_func($callback);
        } elseif (is_string($callback) && strpos($callback, '@') !== false) {
            // إذا كانت الدالة على شكل "Controller@method"
            list($controller, $method) = explode('@', $callback);
            $controller = "App\\Controllers\\" . $controller;

            // إنشاء نسخة من المتحكم وتنفيذ الدالة
            $controllerInstance = new $controller();
            call_user_func([$controllerInstance, $method]);
        } else {
            // في حالة أخرى
            echo "خطأ في تنفيذ المسار";
        }
    }

    // صفحة الخطأ 404
    private function notFound() {
        http_response_code(404);
        echo "الصفحة غير موجودة";
    }

    // الحصول على عنوان URL المطلوب
    private function getRequestUri() {
        $uri = $_SERVER['REQUEST_URI'];
        // إزالة علامات الاستفهام والأمور خلفها
        $uri = parse_url($uri, PHP_URL_PATH);
        // إزالة الشرطة المائلة من البداية والنهاية
        return trim($uri, '/');
    }
}

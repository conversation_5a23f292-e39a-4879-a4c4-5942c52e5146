<?php
// إدارة الجلسات

namespace App\Core;

class Session {
    public function __construct() {
        // تعيين اسم الجلسة
        if (session_name() !== SESSION_NAME) {
            session_name(SESSION_NAME);
        }
    }

    public function start() {
        // بدء الجلسة إذا لم تكن قد بدأت بالفعل
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function set($key, $value) {
        // تعيين قيمة في الجلسة
        $_SESSION[$key] = $value;
    }

    public function get($key, $default = null) {
        // الحصول على قيمة من الجلسة
        return isset($_SESSION[$key]) ? $_SESSION[$key] : $default;
    }

    public function has($key) {
        // التحقق من وجود قيمة في الجلسة
        return isset($_SESSION[$key]);
    }

    public function remove($key) {
        // إزالة قيمة من الجلسة
        if (isset($_SESSION[$key])) {
            unset($_SESSION[$key]);
        }
    }

    public function destroy() {
        // تدمير الجلسة
        session_destroy();
    }

    public function flash($key, $value = null) {
        // رسائل الفلاش
        if ($value) {
            $this->set($key, $value);
        } else {
            $value = $this->get($key);
            $this->remove($key);
            return $value;
        }
    }
}

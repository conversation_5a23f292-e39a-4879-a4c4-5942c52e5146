<?php
// نموذج الإعلان

namespace App\Models;

class Ad extends Model {
    protected $table = 'ads';
    protected $fillable = [
        'user_id', 'category_id', 'title', 'description', 'price', 'currency',
        'city', 'phone', 'images', 'status', 'is_featured', 'expires_at'
    ];

    // الحصول على إعلان مع بيانات المستخدم والفئة
    public function getWithDetails($id) {
        $sql = "SELECT ads.*, users.name as user_name, users.phone as user_phone,
                       categories.name as category_name, categories.icon as category_icon
                FROM {$this->table} ads
                JOIN users ON ads.user_id = users.id
                JOIN categories ON ads.category_id = categories.id
                WHERE ads.id = ?";
        return $this->db->fetch($sql, [$id]);
    }

    // الحصول على جميع الإعلانات مع التفاصيل
    public function getAllWithDetails($status = 'active', $limit = null) {
        $sql = "SELECT ads.*, users.name as user_name,
                       categories.name as category_name, categories.icon as category_icon
                FROM {$this->table} ads
                JOIN users ON ads.user_id = users.id
                JOIN categories ON ads.category_id = categories.id
                WHERE ads.status = ?
                ORDER BY ads.is_featured DESC, ads.created_at DESC";

        if ($limit) {
            $sql .= " LIMIT " . (int)$limit;
        }

        return $this->db->fetchAll($sql, [$status]);
    }

    // الحصول على إعلانات المستخدم
    public function getByUserId($userId, $status = null) {
        $sql = "SELECT ads.*, categories.name as category_name
                FROM {$this->table} ads
                JOIN categories ON ads.category_id = categories.id
                WHERE ads.user_id = ?";
        $params = [$userId];

        if ($status) {
            $sql .= " AND ads.status = ?";
            $params[] = $status;
        }

        $sql .= " ORDER BY ads.created_at DESC";

        return $this->db->fetchAll($sql, $params);
    }

    // الحصول على إعلانات الفئة
    public function getByCategoryId($categoryId, $status = 'active', $limit = null) {
        $sql = "SELECT ads.*, users.name as user_name,
                       categories.name as category_name
                FROM {$this->table} ads
                JOIN users ON ads.user_id = users.id
                JOIN categories ON ads.category_id = categories.id
                WHERE ads.category_id = ? AND ads.status = ?
                ORDER BY ads.is_featured DESC, ads.created_at DESC";

        if ($limit) {
            $sql .= " LIMIT " . (int)$limit;
        }

        return $this->db->fetchAll($sql, [$categoryId, $status]);
    }

    // البحث في الإعلانات
    public function search($keyword, $filters = []) {
        $sql = "SELECT ads.*, users.name as user_name,
                       categories.name as category_name
                FROM {$this->table} ads
                JOIN users ON ads.user_id = users.id
                JOIN categories ON ads.category_id = categories.id
                WHERE ads.status = 'active'";
        $params = [];

        if (!empty($keyword)) {
            $sql .= " AND (ads.title LIKE ? OR ads.description LIKE ?)";
            $searchTerm = "%{$keyword}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        if (!empty($filters['category_id'])) {
            $sql .= " AND ads.category_id = ?";
            $params[] = $filters['category_id'];
        }

        if (!empty($filters['city'])) {
            $sql .= " AND ads.city LIKE ?";
            $params[] = "%{$filters['city']}%";
        }

        if (!empty($filters['min_price'])) {
            $sql .= " AND ads.price >= ?";
            $params[] = $filters['min_price'];
        }

        if (!empty($filters['max_price'])) {
            $sql .= " AND ads.price <= ?";
            $params[] = $filters['max_price'];
        }

        $sql .= " ORDER BY ads.is_featured DESC, ads.created_at DESC";

        return $this->db->fetchAll($sql, $params);
    }

    // زيادة عدد المشاهدات
    public function incrementViews($id) {
        $sql = "UPDATE {$this->table} SET views_count = views_count + 1 WHERE id = ?";
        return $this->db->query($sql, [$id]);
    }

    // تغيير حالة الإعلان
    public function updateStatus($id, $status) {
        return $this->update($id, ['status' => $status]);
    }

    // الحصول على الإعلانات المميزة
    public function getFeatured($limit = 5) {
        $sql = "SELECT ads.*, users.name as user_name,
                       categories.name as category_name
                FROM {$this->table} ads
                JOIN users ON ads.user_id = users.id
                JOIN categories ON ads.category_id = categories.id
                WHERE ads.status = 'active' AND ads.is_featured = 1
                ORDER BY ads.created_at DESC
                LIMIT ?";

        return $this->db->fetchAll($sql, [$limit]);
    }

    // إحصائيات الإعلانات
    public function getStats() {
        $total = $this->count();
        $active = $this->count('status = ?', ['active']);
        $pending = $this->count('status = ?', ['pending']);
        $sold = $this->count('status = ?', ['sold']);
        $featured = $this->count('is_featured = 1');

        return [
            'total' => $total,
            'active' => $active,
            'pending' => $pending,
            'sold' => $sold,
            'featured' => $featured
        ];
    }

    // الحصول على أحدث الإعلانات
    public function getLatest($limit = 10) {
        return $this->getAllWithDetails('active', $limit);
    }

    // الحصول على الإعلانات مع ترقيم الصفحات
    public function getAdsPaginated($page = 1, $perPage = 12, $filters = []) {
        $where = "ads.status = 'active'";
        $params = [];

        if (!empty($filters['search'])) {
            $where .= " AND (ads.title LIKE ? OR ads.description LIKE ?)";
            $searchTerm = "%{$filters['search']}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        if (!empty($filters['category_id'])) {
            $where .= " AND ads.category_id = ?";
            $params[] = $filters['category_id'];
        }

        if (!empty($filters['city'])) {
            $where .= " AND ads.city LIKE ?";
            $params[] = "%{$filters['city']}%";
        }

        $offset = ($page - 1) * $perPage;

        $sql = "SELECT ads.*, users.name as user_name,
                       categories.name as category_name
                FROM {$this->table} ads
                JOIN users ON ads.user_id = users.id
                JOIN categories ON ads.category_id = categories.id
                WHERE {$where}
                ORDER BY ads.is_featured DESC, ads.created_at DESC
                LIMIT {$perPage} OFFSET {$offset}";

        $data = $this->db->fetchAll($sql, $params);

        $countSql = "SELECT COUNT(*) as count
                     FROM {$this->table} ads
                     JOIN users ON ads.user_id = users.id
                     JOIN categories ON ads.category_id = categories.id
                     WHERE {$where}";

        $totalResult = $this->db->fetch($countSql, $params);
        $total = $totalResult['count'];
        $totalPages = ceil($total / $perPage);

        return [
            'data' => $data,
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $total,
            'total_pages' => $totalPages,
            'has_next' => $page < $totalPages,
            'has_prev' => $page > 1
        ];
    }
}

<?php
// نموذج الإعلان

namespace App\Models;

class Ad {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    // إنشاء إعلان جديد
    public function create($data) {
        $this->db->query('INSERT INTO ads (title, description, price, image, category_id, user_id) VALUES (:title, :description, :price, :image, :category_id, :user_id)');
        $this->db->bind(':title', $data['title']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':price', $data['price']);
        $this->db->bind(':image', $data['image']);
        $this->db->bind(':category_id', $data['category_id']);
        $this->db->bind(':user_id', $data['user_id']);

        return $this->db->execute();
    }

    // البحث عن إعلان بالـ ID
    public function findById($id) {
        $this->db->query('SELECT ads.*, users.name as user_name, categories.name as category_name FROM ads JOIN users ON ads.user_id = users.id JOIN categories ON ads.category_id = categories.id WHERE ads.id = :id');
        $this->db->bind(':id', $id);
        $row = $this->db->single();

        return $row ? $row : false;
    }

    // الحصول على جميع الإعلانات
    public function getAll() {
        $this->db->query('SELECT ads.*, users.name as user_name, categories.name as category_name FROM ads JOIN users ON ads.user_id = users.id JOIN categories ON ads.category_id = categories.id ORDER BY ads.created_at DESC');
        return $this->db->resultSet();
    }

    // الحصول على الإعلانات حسب المستخدم
    public function getByUserId($user_id) {
        $this->db->query('SELECT ads.*, users.name as user_name, categories.name as category_name FROM ads JOIN users ON ads.user_id = users.id JOIN categories ON ads.category_id = categories.id WHERE ads.user_id = :user_id ORDER BY ads.created_at DESC');
        $this->db->bind(':user_id', $user_id);
        return $this->db->resultSet();
    }

    // الحصول على الإعلانات حسب القسم
    public function getByCategoryId($category_id) {
        $this->db->query('SELECT ads.*, users.name as user_name, categories.name as category_name FROM ads JOIN users ON ads.user_id = users.id JOIN categories ON ads.category_id = categories.id WHERE ads.category_id = :category_id ORDER BY ads.created_at DESC');
        $this->db->bind(':category_id', $category_id);
        return $this->db->resultSet();
    }

    // البحث في الإعلانات
    public function search($keyword) {
        $this->db->query('SELECT ads.*, users.name as user_name, categories.name as category_name FROM ads JOIN users ON ads.user_id = users.id JOIN categories ON ads.category_id = categories.id WHERE ads.title LIKE :keyword OR ads.description LIKE :keyword ORDER BY ads.created_at DESC');
        $this->db->bind(':keyword', '%' . $keyword . '%');
        return $this->db->resultSet();
    }

    // تحديث بيانات الإعلان
    public function update($id, $data) {
        $this->db->query('UPDATE ads SET title = :title, description = :description, price = :price, image = :image, category_id = :category_id WHERE id = :id');
        $this->db->bind(':id', $id);
        $this->db->bind(':title', $data['title']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':price', $data['price']);
        $this->db->bind(':image', $data['image']);
        $this->db->bind(':category_id', $data['category_id']);

        return $this->db->execute();
    }

    // حذف الإعلان
    public function delete($id) {
        $this->db->query('DELETE FROM ads WHERE id = :id');
        $this->db->bind(':id', $id);

        return $this->db->execute();
    }
}

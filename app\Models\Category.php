<?php
// نموذج الأقسام

namespace App\Models;

class Category {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    // إنشاء قسم جديد
    public function create($data) {
        $this->db->query('INSERT INTO categories (name) VALUES (:name)');
        $this->db->bind(':name', $data['name']);

        return $this->db->execute();
    }

    // البحث عن قسم بالـ ID
    public function findById($id) {
        $this->db->query('SELECT * FROM categories WHERE id = :id');
        $this->db->bind(':id', $id);
        $row = $this->db->single();

        return $row ? $row : false;
    }

    // الحصول على جميع الأقسام
    public function getAll() {
        $this->db->query('SELECT * FROM categories ORDER BY name');
        return $this->db->resultSet();
    }

    // تحديث بيانات القسم
    public function update($id, $data) {
        $this->db->query('UPDATE categories SET name = :name WHERE id = :id');
        $this->db->bind(':id', $id);
        $this->db->bind(':name', $data['name']);

        return $this->db->execute();
    }

    // حذف القسم
    public function delete($id) {
        $this->db->query('DELETE FROM categories WHERE id = :id');
        $this->db->bind(':id', $id);

        return $this->db->execute();
    }
}

<?php
// نموذج الأقسام

namespace App\Models;

class Category extends Model {
    protected $table = 'categories';
    protected $fillable = ['name', 'description', 'icon'];

    // الحصول على الفئات مع عدد الإعلانات
    public function getCategoriesWithAdsCount() {
        $sql = "SELECT c.*, COUNT(a.id) as ads_count
                FROM {$this->table} c
                LEFT JOIN ads a ON c.id = a.category_id AND a.status = 'active'
                GROUP BY c.id
                ORDER BY c.name";
        return $this->db->fetchAll($sql);
    }

    // الحصول على فئة مع عدد الإعلانات
    public function getCategoryWithAdsCount($id) {
        $sql = "SELECT c.*, COUNT(a.id) as ads_count
                FROM {$this->table} c
                LEFT JOIN ads a ON c.id = a.category_id AND a.status = 'active'
                WHERE c.id = ?
                GROUP BY c.id";
        return $this->db->fetch($sql, [$id]);
    }

    // الحصول على الفئات الأكثر استخداماً
    public function getPopularCategories($limit = 5) {
        $sql = "SELECT c.*, COUNT(a.id) as ads_count
                FROM {$this->table} c
                LEFT JOIN ads a ON c.id = a.category_id AND a.status = 'active'
                GROUP BY c.id
                HAVING ads_count > 0
                ORDER BY ads_count DESC
                LIMIT ?";
        return $this->db->fetchAll($sql, [$limit]);
    }

    // البحث في الفئات
    public function searchCategories($keyword) {
        $sql = "SELECT * FROM {$this->table}
                WHERE name LIKE ? OR description LIKE ?
                ORDER BY name";
        $searchTerm = "%{$keyword}%";
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm]);
    }

    // التحقق من وجود اسم فئة
    public function nameExists($name, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE name = ?";
        $params = [$name];

        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }

        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }

    // إحصائيات الفئات
    public function getStats() {
        $total = $this->count();
        $withAds = $this->db->fetch(
            "SELECT COUNT(DISTINCT category_id) as count FROM ads WHERE status = 'active'"
        )['count'];

        return [
            'total' => $total,
            'with_ads' => $withAds,
            'without_ads' => $total - $withAds
        ];
    }

    // الحصول على الفئات للقائمة المنسدلة
    public function getForSelect() {
        $sql = "SELECT id, name FROM {$this->table} ORDER BY name";
        return $this->db->fetchAll($sql);
    }
}

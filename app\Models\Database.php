<?php
// اتصال قاعدة البيانات (PDO)

namespace App\Models;

class Database {
    private $host = DB_HOST;
    private $user = DB_USER;
    private $pass = DB_PASS;
    private $dbname = DB_NAME;

    private $dbh;
    private $error;
    private $stmt;

    public function __construct() {
        // إعداد DSN
        $dsn = 'mysql:host=' . $this->host . ';dbname=' . $this->dbname;
        $options = array(
            \PDO::ATTR_PERSISTENT => true,
            \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION
        );

        // إنشاء اتصال PDO
        try {
            $this->dbh = new \PDO($dsn, $this->user, $this->pass, $options);
        } catch (\PDOException $e) {
            $this->error = $e->getMessage();
            echo "خطأ في الاتصال بقاعدة البيانات: " . $this->error;
            exit;
        }
    }

    // إعداد الاستعلام
    public function query($query) {
        $this->stmt = $this->dbh->prepare($query);
        return $this;
    }

    // ربط القيم
    public function bind($param, $value, $type = null) {
        if (is_null($type)) {
            switch (true) {
                case is_int($value):
                    $type = \PDO::PARAM_INT;
                    break;
                case is_bool($value):
                    $type = \PDO::PARAM_BOOL;
                    break;
                case is_null($value):
                    $type = \PDO::PARAM_NULL;
                    break;
                default:
                    $type = \PDO::PARAM_STR;
            }
        }
        $this->stmt->bindValue($param, $value, $type);
    }

    // تنفيذ الاستعلام
    public function execute() {
        return $this->stmt->execute();
    }

    // الحصول على نتائج متعددة
    public function resultSet() {
        $this->execute();
        return $this->stmt->fetchAll(\PDO::FETCH_OBJ);
    }

    // الحصول على نتيجة واحدة
    public function single() {
        $this->execute();
        return $this->stmt->fetch(\PDO::FETCH_OBJ);
    }

    // الحصول على عدد الصفوف المتأثرة
    public function rowCount() {
        return $this->stmt->rowCount();
    }
}

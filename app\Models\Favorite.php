<?php
// نموذج المفضلة

namespace App\Models;

class Favorite extends Model {
    protected $table = 'favorites';
    protected $fillable = ['user_id', 'ad_id'];

    // إضافة إعلان للمفضلة
    public function addToFavorites($userId, $adId) {
        // التحقق من عدم وجود الإعلان في المفضلة مسبقاً
        if ($this->isFavorite($userId, $adId)) {
            return false;
        }
        
        return $this->create([
            'user_id' => $userId,
            'ad_id' => $adId
        ]);
    }

    // إزالة إعلان من المفضلة
    public function removeFromFavorites($userId, $adId) {
        $where = 'user_id = ? AND ad_id = ?';
        return $this->db->delete($this->table, $where, [$userId, $adId]);
    }

    // التحقق من وجود إعلان في المفضلة
    public function isFavorite($userId, $adId) {
        return $this->exists('user_id = ? AND ad_id = ?', [$userId, $adId]);
    }

    // الحصول على مفضلة المستخدم
    public function getUserFavorites($userId) {
        $sql = "SELECT f.*, a.*, 
                       u.name as user_name,
                       c.name as category_name
                FROM {$this->table} f
                JOIN ads a ON f.ad_id = a.id
                JOIN users u ON a.user_id = u.id
                JOIN categories c ON a.category_id = c.id
                WHERE f.user_id = ? AND a.status = 'active'
                ORDER BY f.created_at DESC";
        
        return $this->db->fetchAll($sql, [$userId]);
    }

    // عدد المفضلة للمستخدم
    public function getUserFavoritesCount($userId) {
        return $this->count('user_id = ?', [$userId]);
    }

    // عدد المفضلة للإعلان
    public function getAdFavoritesCount($adId) {
        return $this->count('ad_id = ?', [$adId]);
    }

    // الحصول على الإعلانات الأكثر إضافة للمفضلة
    public function getMostFavorited($limit = 10) {
        $sql = "SELECT a.*, COUNT(f.id) as favorites_count,
                       u.name as user_name,
                       c.name as category_name
                FROM ads a
                LEFT JOIN {$this->table} f ON a.id = f.ad_id
                JOIN users u ON a.user_id = u.id
                JOIN categories c ON a.category_id = c.id
                WHERE a.status = 'active'
                GROUP BY a.id
                ORDER BY favorites_count DESC, a.created_at DESC
                LIMIT ?";
        
        return $this->db->fetchAll($sql, [$limit]);
    }

    // حذف مفضلة إعلان
    public function deleteAdFavorites($adId) {
        return $this->db->delete($this->table, 'ad_id = ?', [$adId]);
    }

    // حذف مفضلة مستخدم
    public function deleteUserFavorites($userId) {
        return $this->db->delete($this->table, 'user_id = ?', [$userId]);
    }
}

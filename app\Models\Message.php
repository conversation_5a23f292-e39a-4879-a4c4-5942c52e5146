<?php
// نموذج الرسائل

namespace App\Models;

class Message extends Model {
    protected $table = 'messages';
    protected $fillable = ['ad_id', 'sender_id', 'receiver_id', 'message', 'is_read'];

    // الحصول على رسائل المستخدم
    public function getUserMessages($userId, $type = 'received') {
        $column = $type === 'sent' ? 'sender_id' : 'receiver_id';
        
        $sql = "SELECT m.*, 
                       a.title as ad_title,
                       sender.name as sender_name,
                       receiver.name as receiver_name
                FROM {$this->table} m
                JOIN ads a ON m.ad_id = a.id
                JOIN users sender ON m.sender_id = sender.id
                JOIN users receiver ON m.receiver_id = receiver.id
                WHERE m.{$column} = ?
                ORDER BY m.created_at DESC";
        
        return $this->db->fetchAll($sql, [$userId]);
    }

    // الحصول على محادثة بين مستخدمين حول إعلان
    public function getConversation($adId, $userId1, $userId2) {
        $sql = "SELECT m.*, 
                       sender.name as sender_name,
                       receiver.name as receiver_name
                FROM {$this->table} m
                JOIN users sender ON m.sender_id = sender.id
                JOIN users receiver ON m.receiver_id = receiver.id
                WHERE m.ad_id = ? 
                AND ((m.sender_id = ? AND m.receiver_id = ?) 
                     OR (m.sender_id = ? AND m.receiver_id = ?))
                ORDER BY m.created_at ASC";
        
        return $this->db->fetchAll($sql, [$adId, $userId1, $userId2, $userId2, $userId1]);
    }

    // إرسال رسالة
    public function sendMessage($data) {
        return $this->create($data);
    }

    // تحديد الرسالة كمقروءة
    public function markAsRead($id) {
        return $this->update($id, ['is_read' => 1]);
    }

    // تحديد جميع رسائل المستخدم كمقروءة
    public function markAllAsRead($userId) {
        $sql = "UPDATE {$this->table} SET is_read = 1 WHERE receiver_id = ?";
        return $this->db->query($sql, [$userId]);
    }

    // عدد الرسائل غير المقروءة
    public function getUnreadCount($userId) {
        return $this->count('receiver_id = ? AND is_read = 0', [$userId]);
    }

    // الحصول على آخر الرسائل
    public function getRecentMessages($userId, $limit = 10) {
        $sql = "SELECT m.*, 
                       a.title as ad_title,
                       sender.name as sender_name
                FROM {$this->table} m
                JOIN ads a ON m.ad_id = a.id
                JOIN users sender ON m.sender_id = sender.id
                WHERE m.receiver_id = ?
                ORDER BY m.created_at DESC
                LIMIT ?";
        
        return $this->db->fetchAll($sql, [$userId, $limit]);
    }

    // حذف رسائل إعلان
    public function deleteAdMessages($adId) {
        return $this->db->delete($this->table, 'ad_id = ?', [$adId]);
    }
}

<?php
// النموذج الأساسي

namespace App\Models;

use App\Core\Database;

abstract class Model {
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];

    public function __construct() {
        $this->db = Database::getInstance();
    }

    // الحصول على جميع السجلات
    public function getAll($orderBy = 'created_at', $order = 'DESC') {
        $sql = "SELECT * FROM {$this->table} ORDER BY {$orderBy} {$order}";
        return $this->db->fetchAll($sql);
    }

    // الحصول على سجل بواسطة المعرف
    public function getById($id) {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = ?";
        return $this->db->fetch($sql, [$id]);
    }

    // إنشاء سجل جديد
    public function create($data) {
        // تصفية البيانات حسب الحقول المسموحة
        $filteredData = $this->filterData($data);
        
        // إضافة تاريخ الإنشاء والتحديث
        $filteredData['created_at'] = date('Y-m-d H:i:s');
        $filteredData['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->insert($this->table, $filteredData);
    }

    // تحديث سجل
    public function update($id, $data) {
        // تصفية البيانات حسب الحقول المسموحة
        $filteredData = $this->filterData($data);
        
        // إضافة تاريخ التحديث
        $filteredData['updated_at'] = date('Y-m-d H:i:s');
        
        $where = "{$this->primaryKey} = :id";
        $whereParams = ['id' => $id];
        
        return $this->db->update($this->table, $filteredData, $where, $whereParams);
    }

    // حذف سجل
    public function delete($id) {
        $where = "{$this->primaryKey} = ?";
        return $this->db->delete($this->table, $where, [$id]);
    }

    // البحث
    public function search($column, $value, $operator = '=') {
        $sql = "SELECT * FROM {$this->table} WHERE {$column} {$operator} ? ORDER BY created_at DESC";
        return $this->db->fetchAll($sql, [$value]);
    }

    // عد السجلات
    public function count($where = '1=1', $params = []) {
        return $this->db->count($this->table, $where, $params);
    }

    // التحقق من وجود سجل
    public function exists($where, $params = []) {
        return $this->db->exists($this->table, $where, $params);
    }

    // تصفية البيانات حسب الحقول المسموحة
    protected function filterData($data) {
        if (empty($this->fillable)) {
            return $data;
        }
        
        $filtered = [];
        foreach ($this->fillable as $field) {
            if (isset($data[$field])) {
                $filtered[$field] = $data[$field];
            }
        }
        
        return $filtered;
    }

    // الحصول على سجلات مع ترقيم الصفحات
    public function paginate($page = 1, $perPage = 10, $where = '1=1', $params = []) {
        $offset = ($page - 1) * $perPage;
        
        $sql = "SELECT * FROM {$this->table} WHERE {$where} ORDER BY created_at DESC LIMIT {$perPage} OFFSET {$offset}";
        $data = $this->db->fetchAll($sql, $params);
        
        $total = $this->count($where, $params);
        $totalPages = ceil($total / $perPage);
        
        return [
            'data' => $data,
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $total,
            'total_pages' => $totalPages,
            'has_next' => $page < $totalPages,
            'has_prev' => $page > 1
        ];
    }
}

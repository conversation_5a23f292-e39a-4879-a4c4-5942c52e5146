<?php
// نموذج المستخدم

namespace App\Models;

class User extends Model {
    protected $table = 'users';
    protected $fillable = [
        'name', 'email', 'password', 'phone', 'city',
        'is_admin', 'is_active', 'email_verified_at'
    ];

    // البحث عن مستخدم بواسطة البريد الإلكتروني
    public function getByEmail($email) {
        $sql = "SELECT * FROM {$this->table} WHERE email = ?";
        return $this->db->fetch($sql, [$email]);
    }

    // إنشاء مستخدم جديد مع تشفير كلمة المرور
    public function createUser($data) {
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }

        return $this->create($data);
    }

    // تحديث كلمة المرور
    public function updatePassword($id, $newPassword) {
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        return $this->update($id, ['password' => $hashedPassword]);
    }

    // التحقق من كلمة المرور
    public function verifyPassword($password, $hashedPassword) {
        return password_verify($password, $hashedPassword);
    }

    // تسجيل الدخول
    public function login($email, $password) {
        $user = $this->getByEmail($email);

        if (!$user) {
            return false;
        }

        if (!$this->verifyPassword($password, $user['password'])) {
            return false;
        }

        if (!$user['is_active']) {
            return false;
        }

        // إزالة كلمة المرور من البيانات المرجعة
        unset($user['password']);

        return $user;
    }

    // التحقق من وجود بريد إلكتروني
    public function emailExists($email, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE email = ?";
        $params = [$email];

        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }

        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }

    // تفعيل البريد الإلكتروني
    public function verifyEmail($id) {
        return $this->update($id, ['email_verified_at' => date('Y-m-d H:i:s')]);
    }

    // تعطيل/تفعيل المستخدم
    public function toggleActive($id) {
        $user = $this->getById($id);
        if (!$user) {
            return false;
        }

        $newStatus = !$user['is_active'];
        return $this->update($id, ['is_active' => $newStatus]);
    }

    // الحصول على المستخدمين النشطين
    public function getActiveUsers() {
        $sql = "SELECT * FROM {$this->table} WHERE is_active = 1 ORDER BY created_at DESC";
        return $this->db->fetchAll($sql);
    }

    // الحصول على المديرين
    public function getAdmins() {
        $sql = "SELECT * FROM {$this->table} WHERE is_admin = 1 ORDER BY created_at DESC";
        return $this->db->fetchAll($sql);
    }

    // إحصائيات المستخدمين
    public function getStats() {
        $total = $this->count();
        $active = $this->count('is_active = 1');
        $admins = $this->count('is_admin = 1');
        $verified = $this->count('email_verified_at IS NOT NULL');

        return [
            'total' => $total,
            'active' => $active,
            'inactive' => $total - $active,
            'admins' => $admins,
            'verified' => $verified,
            'unverified' => $total - $verified
        ];
    }

    // البحث في المستخدمين
    public function searchUsers($keyword) {
        $sql = "SELECT * FROM {$this->table}
                WHERE name LIKE ? OR email LIKE ? OR city LIKE ?
                ORDER BY created_at DESC";
        $searchTerm = "%{$keyword}%";
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm, $searchTerm]);
    }

    // الحصول على المستخدمين مع ترقيم الصفحات
    public function getUsersPaginated($page = 1, $perPage = 10, $filters = []) {
        $where = '1=1';
        $params = [];

        if (!empty($filters['search'])) {
            $where .= ' AND (name LIKE ? OR email LIKE ?)';
            $searchTerm = "%{$filters['search']}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        if (isset($filters['is_active'])) {
            $where .= ' AND is_active = ?';
            $params[] = $filters['is_active'];
        }

        if (isset($filters['is_admin'])) {
            $where .= ' AND is_admin = ?';
            $params[] = $filters['is_admin'];
        }

        return $this->paginate($page, $perPage, $where, $params);
    }
}

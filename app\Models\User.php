<?php
// نموذج المستخدم

namespace App\Models;

class User {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    // إنشاء مستخدم جديد
    public function create($data) {
        $this->db->query('INSERT INTO users (name, email, password) VALUES (:name, :email, :password)');
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':email', $data['email']);
        $this->db->bind(':password', $data['password']);

        return $this->db->execute();
    }

    // البحث عن مستخدم بالبريد الإلكتروني
    public function findByEmail($email) {
        $this->db->query('SELECT * FROM users WHERE email = :email');
        $this->db->bind(':email', $email);
        $row = $this->db->single();

        return $row ? $row : false;
    }

    // البحث عن مستخدم بالـ ID
    public function findById($id) {
        $this->db->query('SELECT * FROM users WHERE id = :id');
        $this->db->bind(':id', $id);
        $row = $this->db->single();

        return $row ? $row : false;
    }

    // الحصول على جميع المستخدمين
    public function getAll() {
        $this->db->query('SELECT * FROM users');
        return $this->db->resultSet();
    }

    // تحديث بيانات المستخدم
    public function update($id, $data) {
        $this->db->query('UPDATE users SET name = :name, email = :email WHERE id = :id');
        $this->db->bind(':id', $id);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':email', $data['email']);

        return $this->db->execute();
    }

    // حذف المستخدم
    public function delete($id) {
        $this->db->query('DELETE FROM users WHERE id = :id');
        $this->db->bind(':id', $id);

        return $this->db->execute();
    }
}

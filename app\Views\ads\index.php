<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>جميع الإعلانات</h2>
    <?php if (isset($_SESSION['user_id'])): ?>
        <a href="/ads/create" class="btn btn-primary">إنشاء إعلان جديد</a>
    <?php else: ?>
        <a href="/login" class="btn btn-primary">سجل الدخول لإنشاء إعلان</a>
    <?php endif; ?>
</div>

<div class="row">
    <?php foreach ($ads as $ad): ?>
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <?php if ($ad->image): ?>
                    <img src="/uploads/<?php echo $ad->image; ?>" class="card-img-top" alt="<?php echo $ad->title; ?>" style="height: 200px; object-fit: cover;">
                <?php else: ?>
                    <img src="/assets/images/placeholder.jpg" class="card-img-top" alt=" placeholder" style="height: 200px; object-fit: cover;">
                <?php endif; ?>
                <div class="card-body d-flex flex-column">
                    <h5 class="card-title"><?php echo $ad->title; ?></h5>
                    <p class="card-text flex-grow-1"><?php echo substr($ad->description, 0, 100) . '...'; ?></p>
                    <p class="card-text"><strong><?php echo number_format($ad->price, 2); ?> ريال</strong></p>
                    <p class="card-text"><small class="text-muted">القسم: <?php echo $ad->category_name; ?></small></p>
                    <p class="card-text"><small class="text-muted">بواسطة: <?php echo $ad->user_name; ?></small></p>
                    <a href="/ads/<?php echo $ad->id; ?>" class="btn btn-primary mt-auto">عرض التفاصيل</a>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>

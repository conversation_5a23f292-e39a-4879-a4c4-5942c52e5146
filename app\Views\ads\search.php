<div class="row">
    <div class="col-md-3">
        <div class="card">
            <div class="card-header">
                <h5>البحث والتصفية</h5>
            </div>
            <div class="card-body">
                <form method="GET" action="/search">
                    <div class="mb-3">
                        <label for="q" class="form-label">كلمة البحث</label>
                        <input type="text" class="form-control" id="q" name="q" value="<?php echo isset($_GET['q']) ? $_GET['q'] : ''; ?>">
                    </div>
                    <div class="mb-3">
                        <label for="category_id" class="form-label">القسم</label>
                        <select class="form-select" id="category_id" name="category_id">
                            <option value="">جميع الأقسام</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category->id; ?>" <?php echo isset($_GET['category_id']) && $_GET['category_id'] == $category->id ? 'selected' : ''; ?>><?php echo $category->name; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">بحث</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="col-md-9">
        <h2>نتائج البحث</h2>
        <?php if (isset($_GET['q']) || isset($_GET['category_id'])): ?>
            <p>تم العثور على <?php echo count($ads); ?> إعلانات</p>
        <?php endif; ?>

        <div class="row">
            <?php foreach ($ads as $ad): ?>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        <?php if ($ad->image): ?>
                            <img src="/uploads/<?php echo $ad->image; ?>" class="card-img-top" alt="<?php echo $ad->title; ?>" style="height: 200px; object-fit: cover;">
                        <?php else: ?>
                            <img src="/assets/images/placeholder.jpg" class="card-img-top" alt=" placeholder" style="height: 200px; object-fit: cover;">
                        <?php endif; ?>
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title"><?php echo $ad->title; ?></h5>
                            <p class="card-text flex-grow-1"><?php echo substr($ad->description, 0, 100) . '...'; ?></p>
                            <p class="card-text"><strong><?php echo number_format($ad->price, 2); ?> ريال</strong></p>
                            <p class="card-text"><small class="text-muted">القسم: <?php echo $ad->category_name; ?></small></p>
                            <p class="card-text"><small class="text-muted">بواسطة: <?php echo $ad->user_name; ?></small></p>
                            <a href="/ads/<?php echo $ad->id; ?>" class="btn btn-primary mt-auto">عرض التفاصيل</a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

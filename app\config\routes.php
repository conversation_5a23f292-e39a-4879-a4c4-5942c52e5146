<?php
// تعريف المسارات

// الصفحة الرئيسية
$router->add('', 'HomeController@index', 'GET');

// تسجيل الدخول وتسجيل الخروج
$router->add('login', 'AuthController@showLogin', 'GET');
$router->add('login', 'AuthController@login', 'POST');
$router->add('logout', 'AuthController@logout', 'POST');

// التسجيل
$router->add('register', 'AuthController@showRegister', 'GET');
$router->add('register', 'AuthController@register', 'POST');

// عرض الإعلانات
$router->add('ads', 'AdController@index', 'GET');
$router->add('ads/{id}', 'AdController@show', 'GET');

// إضافة إعلان جديد
$router->add('ads/create', 'AdController@create', 'GET');
$router->add('ads/create', 'AdController@store', 'POST');

// تحرير إعلان
$router->add('ads/{id}/edit', 'AdController@edit', 'GET');
$router->add('ads/{id}/edit', 'AdController@update', 'POST');

// حذف إعلان
$router->add('ads/{id}/delete', 'AdController@delete', 'POST');

// البحث والتصفية
$router->add('search', 'SearchController@index', 'GET');

// لوحة التحكم الإدارية
$router->add('admin', 'AdminController@index', 'GET');
$router->add('admin/ads', 'AdminController@ads', 'GET');
$router->add('admin/users', 'AdminController@users', 'GET');

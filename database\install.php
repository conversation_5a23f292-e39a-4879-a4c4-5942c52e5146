<?php
// سكريبت تثبيت قاعدة البيانات

require_once __DIR__ . '/../app/config/constants.php';

try {
    if (defined('DB_TYPE') && DB_TYPE === 'sqlite') {
        // إنشاء مجلد التخزين إذا لم يكن موجوداً
        $storageDir = dirname(DB_PATH);
        if (!is_dir($storageDir)) {
            mkdir($storageDir, 0755, true);
        }

        // الاتصال بـ SQLite
        $pdo = new PDO(
            "sqlite:" . DB_PATH,
            null,
            null,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]
        );

        // تفعيل المفاتيح الخارجية
        $pdo->exec('PRAGMA foreign_keys = ON');

        echo "تم الاتصال بـ SQLite بنجاح\n";
        echo "مسار قاعدة البيانات: " . DB_PATH . "\n";

        // قراءة ملف SQL
        $sql = file_get_contents(__DIR__ . '/setup_sqlite.sql');
    } else {
        // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]
        );

        echo "تم الاتصال بـ MySQL بنجاح\n";

        // قراءة ملف SQL
        $sql = file_get_contents(__DIR__ . '/setup.sql');
    }
    
    if ($sql === false) {
        throw new Exception("لا يمكن قراءة ملف SQL");
    }

    // تنفيذ SQL مباشرة للـ SQLite
    if (defined('DB_TYPE') && DB_TYPE === 'sqlite') {
        $pdo->exec($sql);
        echo "✓ تم تنفيذ جميع الاستعلامات بنجاح\n";
    } else {
        // تقسيم الاستعلامات للـ MySQL
        $statements = array_filter(
            array_map('trim', explode(';', $sql)),
            function($stmt) {
                return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
            }
        );

        echo "تم العثور على " . count($statements) . " استعلام\n";

        // تنفيذ الاستعلامات
        foreach ($statements as $statement) {
            if (!empty(trim($statement))) {
                try {
                    $pdo->exec($statement);
                    echo "✓ تم تنفيذ الاستعلام بنجاح\n";
                } catch (PDOException $e) {
                    echo "✗ خطأ في تنفيذ الاستعلام: " . $e->getMessage() . "\n";
                    echo "الاستعلام: " . substr($statement, 0, 100) . "...\n";
                }
            }
        }
    }

    echo "\n=== تم الانتهاء من تثبيت قاعدة البيانات ===\n";
    echo "قاعدة البيانات: " . DB_NAME . "\n";
    echo "المضيف: " . DB_HOST . "\n";
    echo "المستخدم الإداري: <EMAIL>\n";
    echo "كلمة المرور الافتراضية: password\n";

} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . "\n";
    exit(1);
}

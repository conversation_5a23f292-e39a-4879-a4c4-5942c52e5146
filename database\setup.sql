-- إن<PERSON>اء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS haraj CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE haraj;

-- جدول الفئات
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(100) NOT NULL,
    email VARCHAR(150) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    city VARCHAR(50),
    is_admin BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الإعلانات
CREATE TABLE IF NOT EXISTS ads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    category_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    price DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'SAR',
    city VARCHAR(50),
    phone VARCHAR(20),
    images JSON,
    status ENUM('pending', 'active', 'sold', 'expired', 'rejected') DEFAULT 'pending',
    views_count INT DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT,
    INDEX idx_category (category_id),
    INDEX idx_user (user_id),
    INDEX idx_status (status),
    INDEX idx_city (city),
    INDEX idx_created (created_at)
);

-- جدول الرسائل
CREATE TABLE IF NOT EXISTS messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ad_id INT NOT NULL,
    sender_id INT NOT NULL,
    receiver_id INT NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_ad (ad_id),
    INDEX idx_receiver (receiver_id),
    INDEX idx_created (created_at)
);

-- جدول المفضلة
CREATE TABLE IF NOT EXISTS favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ad_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE,
    UNIQUE KEY unique_favorite (user_id, ad_id)
);

-- إدراج الفئات الأساسية
INSERT INTO categories (name, description, icon) VALUES
('السيارات', 'سيارات جديدة ومستعملة', 'fas fa-car'),
('العقار', 'شقق وفلل وأراضي للبيع والإيجار', 'fas fa-home'),
('الإلكترونيات', 'هواتف وحاسوب وأجهزة إلكترونية', 'fas fa-laptop'),
('الملابس', 'ملابس رجالية ونسائية وأطفال', 'fas fa-tshirt'),
('خدمات', 'خدمات متنوعة', 'fas fa-tools'),
('أثاث', 'أثاث منزلي ومكتبي', 'fas fa-couch'),
('رياضة', 'معدات رياضية وألعاب', 'fas fa-football-ball'),
('كتب', 'كتب ومجلات', 'fas fa-book'),
('حيوانات', 'حيوانات أليفة', 'fas fa-paw'),
('أخرى', 'فئات أخرى متنوعة', 'fas fa-ellipsis-h');

-- إنشاء مستخدم إداري افتراضي
INSERT INTO users (name, email, password, is_admin, is_active, email_verified_at) VALUES
('المدير', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', TRUE, TRUE, NOW());

-- إدراج بعض الإعلانات التجريبية
INSERT INTO ads (user_id, category_id, title, description, price, city, phone, status) VALUES
(1, 1, 'سيارة تويوتا كامري 2020', 'سيارة في حالة ممتازة، قليلة الاستخدام، صيانة دورية منتظمة', 85000.00, 'الرياض', '0501234567', 'active'),
(1, 2, 'شقة للإيجار في حي الملز', 'شقة 3 غرف وصالة، مطبخ مجهز، موقع مميز', 2500.00, 'الرياض', '0501234567', 'active'),
(1, 3, 'آيفون 14 برو ماكس', 'جهاز جديد لم يستخدم، مع جميع الملحقات الأصلية', 4500.00, 'جدة', '0501234567', 'active'),
(1, 4, 'فستان سهرة أنيق', 'فستان سهرة بحالة ممتازة، مقاس M', 350.00, 'الدمام', '0501234567', 'active'),
(1, 5, 'خدمة تنظيف منازل', 'خدمة تنظيف شاملة للمنازل والمكاتب', 200.00, 'الرياض', '0501234567', 'active');

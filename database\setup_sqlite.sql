-- إنشاء جداول SQLite

-- جدول الفئات
CREATE TABLE IF NOT EXISTS categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(150) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    city VARCHAR(50),
    is_admin BOOLEAN DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    email_verified_at DATETIME NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- جدول الإعلانات
CREATE TABLE IF NOT EXISTS ads (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    category_id INTEGER NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    price DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'SAR',
    city VARCHAR(50),
    phone VARCHAR(20),
    images TEXT, -- JSON as TEXT in SQLite
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'sold', 'expired', 'rejected')),
    views_count INTEGER DEFAULT 0,
    is_featured BOOLEAN DEFAULT 0,
    expires_at DATETIME NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT
);

-- جدول الرسائل
CREATE TABLE IF NOT EXISTS messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ad_id INTEGER NOT NULL,
    sender_id INTEGER NOT NULL,
    receiver_id INTEGER NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول المفضلة
CREATE TABLE IF NOT EXISTS favorites (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    ad_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE,
    UNIQUE(user_id, ad_id)
);



-- إدراج الفئات الأساسية
INSERT OR IGNORE INTO categories (name, description, icon) VALUES
('السيارات', 'سيارات جديدة ومستعملة', 'fas fa-car'),
('العقار', 'شقق وفلل وأراضي للبيع والإيجار', 'fas fa-home'),
('الإلكترونيات', 'هواتف وحاسوب وأجهزة إلكترونية', 'fas fa-laptop'),
('الملابس', 'ملابس رجالية ونسائية وأطفال', 'fas fa-tshirt'),
('خدمات', 'خدمات متنوعة', 'fas fa-tools'),
('أثاث', 'أثاث منزلي ومكتبي', 'fas fa-couch'),
('رياضة', 'معدات رياضية وألعاب', 'fas fa-football-ball'),
('كتب', 'كتب ومجلات', 'fas fa-book'),
('حيوانات', 'حيوانات أليفة', 'fas fa-paw'),
('أخرى', 'فئات أخرى متنوعة', 'fas fa-ellipsis-h');

-- إنشاء مستخدم إداري افتراضي (كلمة المرور: password)
INSERT OR IGNORE INTO users (name, email, password, is_admin, is_active, email_verified_at) VALUES
('المدير', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, 1, datetime('now'));

-- إدراج بعض الإعلانات التجريبية
INSERT OR IGNORE INTO ads (user_id, category_id, title, description, price, city, phone, status) VALUES
(1, 1, 'سيارة تويوتا كامري 2020', 'سيارة في حالة ممتازة، قليلة الاستخدام، صيانة دورية منتظمة', 85000.00, 'الرياض', '0501234567', 'active'),
(1, 2, 'شقة للإيجار في حي الملز', 'شقة 3 غرف وصالة، مطبخ مجهز، موقع مميز', 2500.00, 'الرياض', '0501234567', 'active'),
(1, 3, 'آيفون 14 برو ماكس', 'جهاز جديد لم يستخدم، مع جميع الملحقات الأصلية', 4500.00, 'جدة', '0501234567', 'active'),
(1, 4, 'فستان سهرة أنيق', 'فستان سهرة بحالة ممتازة، مقاس M', 350.00, 'الدمام', '0501234567', 'active'),
(1, 5, 'خدمة تنظيف منازل', 'خدمة تنظيف شاملة للمنازل والمكاتب', 200.00, 'الرياض', '0501234567', 'active');

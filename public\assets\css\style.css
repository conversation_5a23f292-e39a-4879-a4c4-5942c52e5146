/* تنسيقات مخصصة لموقع حراج */

body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
}

.jumbotron {
    background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('/assets/images/banner.jpg');
    background-size: cover;
    background-position: center;
}

.card {
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.navbar-brand {
    font-weight: bold;
}

.footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* تنسيقات خاصة بالصور */
.card-img-top {
    height: 200px;
    object-fit: cover;
}

/* تنسيقات خاصة بالنموذج */
.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* تنسيقات خاصة بالجداول */
.table th {
    background-color: #e9ecef;
}

/* تنسيقات خاصة بالأزرار */
.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* تنسيقات خاصة بالتنبيهات */
.alert {
    border-radius: 0.375rem;
}

/* تنسيقات خاصة بالقائمة المنسدلة */
.dropdown-menu {
    border-radius: 0.375rem;
}

/* تنسيقات خاصة بالبطاقات */
.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

/* تنسيقات خاصة بالروابط */
a {
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

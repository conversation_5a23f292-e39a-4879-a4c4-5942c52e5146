// سكريبتات مخصصة لموقع حراج

// تفعيل تولتيب Bootstrap
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// تفعيل البوبوفر Bootstrap
document.addEventListener('DOMContentLoaded', function() {
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
});

// تأكيد الحذف
function confirmDelete(message) {
    return confirm(message || 'هل أنت متأكد من تنفيذ هذا الإجراء؟');
}

// إظهار وإخفاء كلمة المرور
function togglePasswordVisibility(inputId, buttonId) {
    var input = document.getElementById(inputId);
    var button = document.getElementById(buttonId);
    
    if (input.type === 'password') {
        input.type = 'text';
        button.innerHTML = 'إخفاء';
    } else {
        input.type = 'password';
        button.innerHTML = 'إظهار';
    }
}

// تحميل الصور قبل إرسال النموذج
function previewImage(inputId, previewId) {
    var input = document.getElementById(inputId);
    var preview = document.getElementById(previewId);
    
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
        }
        
        reader.readAsDataURL(input.files[0]);
    }
}

// البحث في الوقت الفعلي
function liveSearch(inputId, resultListId, url) {
    var input = document.getElementById(inputId);
    var resultList = document.getElementById(resultListId);
    
    input.addEventListener('keyup', function() {
        var query = this.value;
        
        if (query.length > 2) {
            fetch(url + '?q=' + query)
                .then(response => response.json())
                .then(data => {
                    resultList.innerHTML = '';
                    data.forEach(item => {
                        var li = document.createElement('li');
                        li.className = 'list-group-item';
                        li.textContent = item.name;
                        resultList.appendChild(li);
                    });
                });
        } else {
            resultList.innerHTML = '';
        }
    });
}

// تحديث العداد
function updateCounter(elementId, maxValue) {
    var element = document.getElementById(elementId);
    var currentLength = element.value.length;
    var counter = document.getElementById(elementId + '-counter');
    
    if (counter) {
        counter.textContent = currentLength + '/' + maxValue;
        
        if (currentLength > maxValue) {
            counter.style.color = 'red';
        } else {
            counter.style.color = 'inherit';
        }
    }
}

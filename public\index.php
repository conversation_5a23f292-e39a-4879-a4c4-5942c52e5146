<?php
// نقطة الدخول الوحيدة للتطبيق

// تفعيل عرض الأخطاء للتطوير
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // تحميل ملفات التكوين
    require_once __DIR__ . '/../app/config/constants.php';

    // تحميل الـ Autoloader البسيط
    spl_autoload_register(function ($class) {
        // تحويل namespace إلى مسار ملف
        $file = __DIR__ . '/../' . str_replace(['App\\', '\\'], ['app/', '/'], $class) . '.php';
        if (file_exists($file)) {
            require_once $file;
        }
    });

    // تهيئة التطبيق
    $app = new App\Core\Application();

    // تشغيل التطبيق
    $app->run();

} catch (Exception $e) {
    echo "خطأ في التطبيق: " . $e->getMessage() . "<br>";
    echo "في الملف: " . $e->getFile() . " السطر: " . $e->getLine() . "<br>";
} catch (Error $e) {
    echo "خطأ فادح: " . $e->getMessage() . "<br>";
    echo "في الملف: " . $e->getFile() . " السطر: " . $e->getLine() . "<br>";
}

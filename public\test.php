<?php
// ملف اختبار بسيط

echo "PHP is working!<br>";
echo "Current directory: " . __DIR__ . "<br>";
echo "File exists check:<br>";

// تحقق من وجود الملفات
$files = [
    '../app/config/constants.php',
    '../app/Core/Application.php',
    '../app/Core/Router.php',
    '../app/Core/Session.php'
];

foreach ($files as $file) {
    $fullPath = __DIR__ . '/' . $file;
    echo "- $file: " . (file_exists($fullPath) ? 'موجود' : 'غير موجود') . "<br>";
}

// اختبار تحميل الثوابت
try {
    require_once __DIR__ . '/../app/config/constants.php';
    echo "<br>Constants loaded successfully<br>";
    echo "DB_HOST: " . DB_HOST . "<br>";
    echo "APP_NAME: " . APP_NAME . "<br>";
} catch (Exception $e) {
    echo "<br>Error loading constants: " . $e->getMessage() . "<br>";
}

// اختبار الـ autoloader
try {
    spl_autoload_register(function ($class) {
        $file = __DIR__ . '/../' . str_replace(['App\\', '\\'], ['app/', '/'], $class) . '.php';
        echo "Trying to load: $file<br>";
        if (file_exists($file)) {
            require_once $file;
            echo "Loaded: $class<br>";
        } else {
            echo "File not found for: $class<br>";
        }
    });
    
    echo "<br>Autoloader registered<br>";
    
    // اختبار إنشاء كائن Session
    $session = new App\Core\Session();
    echo "Session object created successfully<br>";
    
} catch (Exception $e) {
    echo "<br>Error with autoloader or Session: " . $e->getMessage() . "<br>";
}
